'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Question, getCategoryById, mockExamConfig, getCategoryPassingScore } from '../data';
import Image from 'next/image';

interface ExamResult {
  totalQuestions: number;
  correctAnswers: number;
  incorrectAnswers: number;
  score: number;
  passingScore: number;
  passed: boolean;
  examType: string;
  examTitle: string;
  userName: string;
  userEmail: string;
}

export default function ResultsPage() {
  const searchParams = useSearchParams();
  const [result, setResult] = useState<ExamResult | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [userAnswers, setUserAnswers] = useState<{ [key: number]: number }>({});
  const [showDetails, setShowDetails] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const examType = searchParams.get('type');
    const categoryId = searchParams.get('category');
    const userName = searchParams.get('name') || '';
    const userEmail = searchParams.get('email') || '';
    const answersParam = searchParams.get('answers');
    const questionsParam = searchParams.get('questions');

    if (!answersParam || !questionsParam) {
      setIsLoading(false);
      return;
    }

    try {
      const answers = JSON.parse(answersParam);
      const questionsData = JSON.parse(questionsParam);
      
      setQuestions(questionsData);
      setUserAnswers(answers);

      // Calculate results
      let correctCount = 0;
      questionsData.forEach((question: Question, index: number) => {
        if (answers[index] === question.correctAnswer) {
          correctCount++;
        }
      });

      const totalQuestions = questionsData.length;
      const score = Math.round((correctCount / totalQuestions) * 100);
      
      let passingScore = 90; // Default 90%
      let examTitle = '';
      
      if (examType === 'mock') {
        passingScore = Math.round((mockExamConfig.passingScore / mockExamConfig.totalQuestions) * 100);
        examTitle = 'ข้อสอบเสมือนจริง';
      } else if (examType === 'category' && categoryId) {
        const category = getCategoryById(categoryId);
        const categoryPassingCount = getCategoryPassingScore(categoryId);
        passingScore = Math.round((categoryPassingCount / totalQuestions) * 100);
        examTitle = category?.name || 'ข้อสอบตามหมวดหมู่';
      }

      const examResult: ExamResult = {
        totalQuestions,
        correctAnswers: correctCount,
        incorrectAnswers: totalQuestions - correctCount,
        score,
        passingScore,
        passed: score >= passingScore,
        examType: examType || '',
        examTitle,
        userName,
        userEmail
      };

      setResult(examResult);
    } catch (error) {
      console.error('Error parsing exam data:', error);
    }
    
    setIsLoading(false);
  }, [searchParams]);

  const getScoreColor = (score: number, passingScore: number) => {
    if (score >= passingScore) {
      return 'text-green-600';
    } else if (score >= passingScore - 10) {
      return 'text-yellow-600';
    } else {
      return 'text-red-600';
    }
  };

  const getResultIcon = (passed: boolean) => {
    return passed ? '🎉' : '😔';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">กำลังคำนวณผล...</p>
        </div>
      </div>
    );
  }

  if (!result) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-red-600 mb-4">ไม่พบข้อมูลผลการสอบ</h1>
        <p className="text-gray-600 mb-6">ไม่สามารถโหลดผลการสอบได้ กรุณาลองใหม่อีกครั้ง</p>
        <button
          onClick={() => window.location.href = '/'}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          กลับหน้าหลัก
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Result Header */}
      <div className="bg-white rounded-lg shadow-lg p-8 mb-6 text-center">
        <div className="text-6xl mb-4">{getResultIcon(result.passed)}</div>
        <h1 className="text-3xl font-bold text-gray-800 mb-2">
          {result.passed ? 'ยินดีด้วย! คุณสอบผ่าน' : 'เสียใจด้วย คุณสอบไม่ผ่าน'}
        </h1>
        <h2 className="text-xl text-gray-600 mb-6">{result.examTitle}</h2>
        
        <div className="grid md:grid-cols-2 gap-6 mb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-700 mb-2">ข้อมูลผู้เข้าสอบ</h3>
            <p className="text-gray-600">ชื่อ: {result.userName}</p>
            <p className="text-gray-600">อีเมล: {result.userEmail}</p>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-700 mb-2">ผลการสอบ</h3>
            <p className={`text-2xl font-bold ${getScoreColor(result.score, result.passingScore)}`}>
              {result.score}%
            </p>
            <p className="text-sm text-gray-600">
              เกณฑ์ผ่าน: {result.passingScore}%
            </p>
          </div>
        </div>

        {/* Score Details */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600">{result.totalQuestions}</div>
            <div className="text-sm text-gray-600">ข้อสอบทั้งหมด</div>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600">{result.correctAnswers}</div>
            <div className="text-sm text-gray-600">ตอบถูก</div>
          </div>
          <div className="bg-red-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-red-600">{result.incorrectAnswers}</div>
            <div className="text-sm text-gray-600">ตอบผิด</div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            {showDetails ? 'ซ่อนรายละเอียด' : 'ดูรายละเอียดคำตอบ'}
          </button>
          <button
            onClick={() => window.location.href = '/'}
            className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
          >
            กลับหน้าหลัก
          </button>
          <button
            onClick={() => window.location.reload()}
            className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
          >
            ทำข้อสอบใหม่
          </button>
        </div>
      </div>

      {/* Detailed Results */}
      {showDetails && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-2xl font-bold text-gray-800 mb-6">รายละเอียดคำตอบ</h3>
          <div className="space-y-6">
            {questions.map((question, index) => {
              const userAnswer = userAnswers[index];
              const isCorrect = userAnswer === question.correctAnswer;
              
              return (
                <div key={index} className={`border-l-4 pl-4 ${
                  isCorrect ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'
                }`}>
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-semibold text-gray-800">
                      {question.numbers}
                    </h4>
                    <span className={`px-2 py-1 rounded text-sm font-medium ${
                      isCorrect ? 'bg-green-200 text-green-800' : 'bg-red-200 text-red-800'
                    }`}>
                      {isCorrect ? 'ถูก' : 'ผิด'}
                    </span>
                  </div>
                  
                  {/* Question Content */}
                  <div className="mb-3">
                    {question.question.map((item, qIndex) => (
                      <div key={qIndex}>
                        {item.text && <p className="text-gray-700 mb-2">{item.text}</p>}
                        {item.image && (
                          <div className="mb-2">
                            <Image
                              src={item.image}
                              alt={`Question ${index + 1} image`}
                              width={300}
                              height={200}
                              className="rounded-md object-contain"
                              unoptimized
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                  
                  {/* Answer Options */}
                  <div className="space-y-1 text-sm">
                    {question.options.map((option, optIndex) => {
                      const isUserAnswer = userAnswer === optIndex;
                      const isCorrectAnswer = question.correctAnswer === optIndex;
                      
                      return (
                        <div key={optIndex} className={`p-2 rounded ${
                          isCorrectAnswer ? 'bg-green-100 border border-green-300' :
                          isUserAnswer ? 'bg-red-100 border border-red-300' : 'bg-gray-50'
                        }`}>
                          <span className="font-medium">
                            {String.fromCharCode(65 + optIndex)}.
                          </span>
                          {option.text && <span className="ml-2">{option.text}</span>}
                          {isCorrectAnswer && <span className="ml-2 text-green-600 font-medium">(คำตอบที่ถูก)</span>}
                          {isUserAnswer && !isCorrectAnswer && <span className="ml-2 text-red-600 font-medium">(คำตอบของคุณ)</span>}
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
