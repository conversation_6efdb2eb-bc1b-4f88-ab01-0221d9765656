import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ข้อสอบใบขับขี่ - ระบบทดสอบออนไลน์",
  description: "ระบบทดสอบข้อสอบใบขับขี่ออนไลน์ พร้อมข้อสอบเสมือนจริงและข้อสอบตามหมวดหมู่",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="th">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gray-50 min-h-screen`}
      >
        <div className="min-h-screen flex flex-col">
          <header className="bg-blue-600 text-white shadow-lg">
            <div className="container mx-auto px-4 py-4">
              <h1 className="text-2xl font-bold text-center">
                ระบบทดสอบข้อสอบใบขับขี่
              </h1>
            </div>
          </header>
          <main className="flex-1 container mx-auto px-4 py-8">
            {children}
          </main>
          <footer className="bg-gray-800 text-white py-4">
            <div className="container mx-auto px-4 text-center">
              <p>&copy; 2024 ระบบทดสอบข้อสอบใบขับขี่. สงวนลิขสิทธิ์.</p>
            </div>
          </footer>
        </div>
      </body>
    </html>
  );
}
