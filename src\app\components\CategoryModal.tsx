'use client';

import { useState } from 'react';
import { getCategoryById, getCategoryPassingScore, getCategoryTimeLimit } from '../data';

interface CategoryModalProps {
  categoryId: string;
  onClose: () => void;
}

export default function CategoryModal({ categoryId, onClose }: CategoryModalProps) {
  const [showUserForm, setShowUserForm] = useState(false);
  const [userInfo, setUserInfo] = useState({
    name: '',
    email: ''
  });

  const category = getCategoryById(categoryId);
  const passingScore = getCategoryPassingScore(categoryId);
  const timeLimit = getCategoryTimeLimit(categoryId);

  if (!category) {
    return null;
  }

  const handleStartTest = () => {
    setShowUserForm(true);
  };

  const handleSubmitUserInfo = (e: React.FormEvent) => {
    e.preventDefault();
    if (userInfo.name.trim() && userInfo.email.trim()) {
      const params = new URLSearchParams({
        type: 'category',
        category: categoryId,
        name: userInfo.name,
        email: userInfo.email
      });
      window.location.href = `/exam?${params.toString()}`;
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUserInfo({
      ...userInfo,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        {!showUserForm ? (
          <>
            {/* Initial Modal */}
            <div className="text-center">
              <div className="text-6xl mb-4">{category.icon}</div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                {category.name}
              </h2>
              <p className="text-gray-600 mb-6">
                {category.description}
              </p>
              <div className="text-gray-600 mb-6 space-y-2">
                <p>จำนวน {category.count} ข้อ</p>
                <p>ผ่านเกณฑ์ 90% หรือ {passingScore} ข้อ</p>
                <p>จับเวลา {timeLimit} นาที</p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={onClose}
                  className="flex-1 bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-400 transition-colors"
                >
                  ยกเลิก
                </button>
                <button
                  onClick={handleStartTest}
                  className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                  เริ่มทดสอบ
                </button>
              </div>
            </div>
          </>
        ) : (
          <>
            {/* User Info Form */}
            <div className="text-center mb-6">
              <div className="text-4xl mb-4">👤</div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                กรอกข้อมูลผู้เข้าสอบ
              </h2>
              <p className="text-gray-600 mb-2">
                {category.name}
              </p>
              <p className="text-sm text-gray-500">
                กรุณากรอกชื่อและอีเมลเพื่อเริ่มทำข้อสอบ
              </p>
            </div>
            
            <form onSubmit={handleSubmitUserInfo} className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  ชื่อ-นามสกุล *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={userInfo.name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="กรอกชื่อ-นามสกุล"
                />
              </div>
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  อีเมล *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={userInfo.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="กรอกอีเมล"
                />
              </div>
              
              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowUserForm(false)}
                  className="flex-1 bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-400 transition-colors"
                >
                  ย้อนกลับ
                </button>
                <button
                  type="submit"
                  className="flex-1 bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors"
                >
                  ทดสอบ
                </button>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
}
