'use client';

import { useState, useEffect } from 'react';
import { Question, getCategoryById, mockExamConfig, getCategoryPassingScore } from '../data';
import Image from 'next/image';

interface ExamResult {
  totalQuestions: number;
  correctAnswers: number;
  incorrectAnswers: number;
  score: number;
  passingScore: number;
  passed: boolean;
  examType: string;
  examTitle: string;
  userName: string;
  userEmail: string;
}

export default function ResultsPage() {
  const [result, setResult] = useState<ExamResult | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [userAnswers, setUserAnswers] = useState<{ [key: number]: number }>({});
  const [showDetails, setShowDetails] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    try {
      // Get exam data from localStorage
      const examDataStr = localStorage.getItem('examResult');
      if (!examDataStr) {
        console.error('No exam data found in localStorage');
        setIsLoading(false);
        return;
      }

      const examData = JSON.parse(examDataStr);
      const { type: examType, category: categoryId, name: userName, email: userEmail, answers, questions: questionsData, examTitle } = examData;

      if (!answers || !questionsData) {
        console.error('Invalid exam data structure');
        setIsLoading(false);
        return;
      }

      setQuestions(questionsData);
      setUserAnswers(answers);

      // Calculate results
      let correctCount = 0;
      questionsData.forEach((question: Question, index: number) => {
        if (answers[index] === question.correctAnswer) {
          correctCount++;
        }
      });

      const totalQuestions = questionsData.length;
      const score = Math.round((correctCount / totalQuestions) * 100);

      let passingScore = 90; // Default 90%
      let finalExamTitle = examTitle || '';

      if (examType === 'mock') {
        passingScore = Math.round((mockExamConfig.passingScore / mockExamConfig.totalQuestions) * 100);
        finalExamTitle = 'ข้อสอบเสมือนจริง';
      } else if (examType === 'category' && categoryId) {
        const category = getCategoryById(categoryId);
        const categoryPassingCount = getCategoryPassingScore(categoryId);
        passingScore = Math.round((categoryPassingCount / totalQuestions) * 100);
        finalExamTitle = category?.name || 'ข้อสอบตามหมวดหมู่';
      }

      const examResult: ExamResult = {
        totalQuestions,
        correctAnswers: correctCount,
        incorrectAnswers: totalQuestions - correctCount,
        score,
        passingScore,
        passed: score >= passingScore,
        examType: examType || '',
        examTitle: finalExamTitle,
        userName: userName || '',
        userEmail: userEmail || ''
      };

      setResult(examResult);

      // Clear localStorage after processing
      localStorage.removeItem('examResult');

    } catch (error) {
      console.error('Error parsing exam data:', error);
    }

    setIsLoading(false);
  }, []);

  const getScoreColor = (score: number, passingScore: number) => {
    if (score >= passingScore) {
      return 'text-green-600';
    } else if (score >= passingScore - 10) {
      return 'text-yellow-600';
    } else {
      return 'text-red-600';
    }
  };

  const getResultIcon = (passed: boolean) => {
    return passed ? '🎉' : '😔';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">กำลังคำนวณผล...</p>
        </div>
      </div>
    );
  }

  if (!result) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-red-600 mb-4">ไม่พบข้อมูลผลการสอบ</h1>
        <p className="text-gray-600 mb-6">ไม่สามารถโหลดผลการสอบได้ กรุณาลองใหม่อีกครั้ง</p>
        <button
          onClick={() => window.location.href = '/'}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          กลับหน้าหลัก
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Result Header */}
      <div className="bg-white rounded-lg shadow-lg p-8 mb-6 text-center">
        <div className="text-6xl mb-4">{getResultIcon(result.passed)}</div>
        <h1 className="text-3xl font-bold text-gray-800 mb-2">
          {result.passed ? 'ยินดีด้วย! คุณสอบผ่าน' : 'เสียใจด้วย คุณสอบไม่ผ่าน'}
        </h1>
        <h2 className="text-xl text-gray-600 mb-6">{result.examTitle}</h2>
        
        <div className="grid md:grid-cols-2 gap-6 mb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-700 mb-2">ข้อมูลผู้เข้าสอบ</h3>
            <p className="text-gray-600">ชื่อ: {result.userName}</p>
            <p className="text-gray-600">อีเมล: {result.userEmail}</p>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-700 mb-2">ผลการสอบ</h3>
            <p className={`text-2xl font-bold ${getScoreColor(result.score, result.passingScore)}`}>
              {result.score}%
            </p>
            <p className="text-sm text-gray-600">
              เกณฑ์ผ่าน: {result.passingScore}%
            </p>
          </div>
        </div>

        {/* Score Details */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-3 md:p-4 text-center">
            <div className="text-xl md:text-2xl font-bold text-blue-600">{result.totalQuestions}</div>
            <div className="text-xs md:text-sm text-gray-600">ข้อสอบทั้งหมด</div>
          </div>
          <div className="bg-green-50 rounded-lg p-3 md:p-4 text-center">
            <div className="text-xl md:text-2xl font-bold text-green-600">{result.correctAnswers}</div>
            <div className="text-xs md:text-sm text-gray-600">ตอบถูก</div>
          </div>
          <div className="bg-red-50 rounded-lg p-3 md:p-4 text-center">
            <div className="text-xl md:text-2xl font-bold text-red-600">{result.incorrectAnswers}</div>
            <div className="text-xs md:text-sm text-gray-600">ตอบผิด</div>
          </div>
          <div className="bg-yellow-50 rounded-lg p-3 md:p-4 text-center">
            <div className="text-xl md:text-2xl font-bold text-yellow-600">
              {result.totalQuestions - Object.keys(userAnswers).length}
            </div>
            <div className="text-xs md:text-sm text-gray-600">ไม่ได้ตอบ</div>
          </div>
        </div>

        {/* Pass/Fail Status */}
        <div className={`p-4 rounded-lg mb-6 text-center ${
          result.passed ? 'bg-green-100 border border-green-300' : 'bg-red-100 border border-red-300'
        }`}>
          <div className={`text-lg font-bold ${result.passed ? 'text-green-800' : 'text-red-800'}`}>
            {result.passed ? '🎉 ยินดีด้วย! คุณสอบผ่าน' : '😔 เสียใจด้วย คุณสอบไม่ผ่าน'}
          </div>
          <div className="text-sm text-gray-600 mt-1">
            คะแนนของคุณ: {result.score}% (เกณฑ์ผ่าน: {result.passingScore}%)
          </div>
          {!result.passed && (
            <div className="text-sm text-gray-600 mt-2">
              คุณต้องได้คะแนนอย่างน้อย {Math.ceil((result.passingScore / 100) * result.totalQuestions)} ข้อ
              เพื่อผ่านการสอบ
            </div>
          )}
        </div>

        {/* Performance Analysis */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h4 className="font-semibold text-gray-800 mb-3 text-center">การวิเคราะห์ผลงาน</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-blue-600">
                {Math.round((result.correctAnswers / result.totalQuestions) * 100)}%
              </div>
              <div className="text-sm text-gray-600">อัตราความถูกต้อง</div>
            </div>
            <div>
              <div className="text-lg font-bold text-purple-600">
                {Object.keys(userAnswers).length}
              </div>
              <div className="text-sm text-gray-600">ข้อที่ตอบ</div>
            </div>
            <div>
              <div className={`text-lg font-bold ${result.passed ? 'text-green-600' : 'text-red-600'}`}>
                {result.passed ? 'ผ่าน' : 'ไม่ผ่าน'}
              </div>
              <div className="text-sm text-gray-600">ผลการสอบ</div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 md:px-6 py-3 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-md"
          >
            {showDetails ? '📋 ซ่อนรายละเอียด' : '📋 ดูรายละเอียดคำตอบ'}
          </button>
          <button
            onClick={() => window.location.href = '/'}
            className="bg-gradient-to-r from-gray-600 to-gray-700 text-white px-4 md:px-6 py-3 rounded-lg hover:from-gray-700 hover:to-gray-800 transition-all duration-300 shadow-md"
          >
            🏠 กลับหน้าหลัก
          </button>
          <button
            onClick={() => window.location.href = '/'}
            className="bg-gradient-to-r from-green-600 to-green-700 text-white px-4 md:px-6 py-3 rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-md"
          >
            🔄 ทำข้อสอบใหม่
          </button>
        </div>
      </div>

      {/* Detailed Results */}
      {showDetails && (
        <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 animate-fade-in">
          <h3 className="text-xl md:text-2xl font-bold text-gray-800 mb-4 md:mb-6">รายละเอียดคำตอบ</h3>

          {/* Summary Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="text-center">
              <div className="text-lg md:text-xl font-bold text-blue-600">{result?.totalQuestions}</div>
              <div className="text-xs md:text-sm text-gray-600">ข้อทั้งหมด</div>
            </div>
            <div className="text-center">
              <div className="text-lg md:text-xl font-bold text-green-600">{result?.correctAnswers}</div>
              <div className="text-xs md:text-sm text-gray-600">ตอบถูก</div>
            </div>
            <div className="text-center">
              <div className="text-lg md:text-xl font-bold text-red-600">{result?.incorrectAnswers}</div>
              <div className="text-xs md:text-sm text-gray-600">ตอบผิด</div>
            </div>
            <div className="text-center">
              <div className={`text-lg md:text-xl font-bold ${result?.passed ? 'text-green-600' : 'text-red-600'}`}>
                {result?.score}%
              </div>
              <div className="text-xs md:text-sm text-gray-600">คะแนน</div>
            </div>
          </div>

          <div className="space-y-4 md:space-y-6">
            {questions.map((question, index) => {
              const userAnswer = userAnswers[index];
              const isCorrect = userAnswer === question.correctAnswer;
              const hasAnswered = userAnswer !== undefined;

              return (
                <div key={index} className={`border-l-4 pl-3 md:pl-4 rounded-r-lg ${
                  !hasAnswered ? 'border-gray-400 bg-gray-50' :
                  isCorrect ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'
                } p-3 md:p-4`}>
                  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-3">
                    <h4 className="font-semibold text-gray-800 text-sm md:text-base mb-2 sm:mb-0">
                      {question.numbers}
                    </h4>
                    <div className="flex gap-2">
                      <span className={`px-2 py-1 rounded text-xs md:text-sm font-medium ${
                        !hasAnswered ? 'bg-gray-200 text-gray-700' :
                        isCorrect ? 'bg-green-200 text-green-800' : 'bg-red-200 text-red-800'
                      }`}>
                        {!hasAnswered ? 'ไม่ได้ตอบ' : isCorrect ? '✓ ถูก' : '✗ ผิด'}
                      </span>
                      {hasAnswered && (
                        <span className="px-2 py-1 rounded text-xs md:text-sm bg-blue-100 text-blue-800">
                          ตอบ: {String.fromCharCode(65 + userAnswer)}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Question Content */}
                  <div className="mb-4 p-3 bg-white rounded-lg border">
                    {question.question.map((item, qIndex) => (
                      <div key={qIndex}>
                        {item.text && <p className="text-gray-700 mb-2 text-sm md:text-base leading-relaxed">{item.text}</p>}
                        {item.image && (
                          <div className="mb-2 flex justify-center">
                            <Image
                              src={item.image}
                              alt={`Question ${index + 1} image`}
                              width={300}
                              height={200}
                              className="rounded-md object-contain max-w-full h-auto"
                              unoptimized
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Answer Options */}
                  <div className="space-y-2">
                    <h5 className="font-medium text-gray-700 text-sm">ตัวเลือก:</h5>
                    {question.options.map((option, optIndex) => {
                      const isUserAnswer = userAnswer === optIndex;
                      const isCorrectAnswer = question.correctAnswer === optIndex;

                      let bgColor = 'bg-gray-50 border-gray-200';
                      let textColor = 'text-gray-700';
                      let icon = '';

                      if (isCorrectAnswer) {
                        bgColor = 'bg-green-100 border-green-300';
                        textColor = 'text-green-800';
                        icon = '✓';
                      } else if (isUserAnswer && !isCorrectAnswer) {
                        bgColor = 'bg-red-100 border-red-300';
                        textColor = 'text-red-800';
                        icon = '✗';
                      }

                      return (
                        <div key={optIndex} className={`p-3 rounded-lg border ${bgColor} ${textColor} transition-all`}>
                          <div className="flex items-start space-x-2">
                            <span className="font-bold text-sm md:text-base flex-shrink-0">
                              {String.fromCharCode(65 + optIndex)}.
                            </span>
                            <div className="flex-1">
                              {option.text && <span className="text-sm md:text-base">{option.text}</span>}
                              {option.image && (
                                <div className="mt-2">
                                  <Image
                                    src={option.image}
                                    alt={`Option ${String.fromCharCode(65 + optIndex)}`}
                                    width={150}
                                    height={100}
                                    className="rounded-md object-contain"
                                    unoptimized
                                  />
                                </div>
                              )}
                            </div>
                            {icon && (
                              <span className="font-bold text-lg flex-shrink-0">
                                {icon}
                              </span>
                            )}
                          </div>

                          {/* Status Labels */}
                          <div className="mt-2 flex flex-wrap gap-2">
                            {isCorrectAnswer && (
                              <span className="inline-block px-2 py-1 bg-green-200 text-green-800 text-xs rounded-full font-medium">
                                คำตอบที่ถูก
                              </span>
                            )}
                            {isUserAnswer && (
                              <span className={`inline-block px-2 py-1 text-xs rounded-full font-medium ${
                                isCorrectAnswer ? 'bg-blue-200 text-blue-800' : 'bg-red-200 text-red-800'
                              }`}>
                                คำตอบของคุณ
                              </span>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
