'use client';

import { useState } from 'react';
import { categories, mockExamConfig } from './data';
import MockExamModal from './components/MockExamModal';
import CategoryModal from './components/CategoryModal';

export default function Home() {
  const [showMockExamModal, setShowMockExamModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [showCategories, setShowCategories] = useState(false);
  const [showStudyCategories, setShowStudyCategories] = useState(false);

  return (
    <div className="max-w-6xl mx-auto">
      {/* Hero Section */}
      <div className="text-center mb-8 md:mb-12 animate-fade-in">
        <h2 className="text-2xl md:text-4xl font-bold text-gray-800 mb-4">
          ยินดีต้อนรับสู่ระบบทดสอบข้อสอบใบขับขี่
        </h2>
        <p className="text-base md:text-lg text-gray-600 mb-6 md:mb-8 px-4">
          เลือกรูปแบบการทดสอบที่คุณต้องการ
        </p>
      </div>

      {/* Main Options */}
      <div className="grid md:grid-cols-3 gap-6 md:gap-8 mb-8 md:mb-12">
        {/* Mock Exam Card */}
        <div className="bg-white rounded-lg shadow-lg p-6 md:p-8 hover:shadow-xl transition-all duration-300 hover:scale-105 animate-fade-in">
          <div className="text-center">
            <div className="text-4xl md:text-6xl mb-4">📝</div>
            <h3 className="text-xl md:text-2xl font-bold text-gray-800 mb-4">
              ข้อสอบเสมือนจริง
            </h3>
            <div className="text-left text-sm md:text-base text-gray-600 mb-6 space-y-2">
              <p>• จำนวน {mockExamConfig.totalQuestions} ข้อ</p>
              <p>• ผ่านเกณฑ์ 90% หรือ {mockExamConfig.passingScore} ข้อ</p>
              <p>• จับเวลา {mockExamConfig.timeLimit} นาที</p>
            </div>
            <button
              onClick={() => setShowMockExamModal(true)}
              className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 md:px-8 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 w-full shadow-md hover:shadow-lg"
            >
              เริ่มทดสอบ
            </button>
          </div>
        </div>

        {/* Category Exam Card */}
        <button
          onClick={() => setShowCategories(!showCategories)}
          className="bg-white rounded-lg shadow-lg p-6 md:p-8 hover:shadow-xl transition-all duration-300 hover:scale-105 animate-fade-in w-full text-left"
        >
          <div className="text-center">
            <div className="text-4xl md:text-6xl mb-4">📚</div>
            <h3 className="text-xl md:text-2xl font-bold text-gray-800 mb-4">
              ข้อสอบตามหมวดหมู่
            </h3>
            <div className="text-sm md:text-base text-gray-600 mb-6">
              <p>เลือกหมวดหมู่ที่ต้องการทดสอบ</p>
              <p className="hidden md:block">จำนวนข้อสอบและเวลาขึ้นอยู่กับหมวดหมู่</p>
            </div>
            <div className="text-xs md:text-sm text-gray-500">
              {showCategories ? '🔼 คลิกเพื่อซ่อนหมวดหมู่' : '🔽 คลิกเพื่อเลือกหมวดหมู่'}
            </div>
          </div>
        </button>

        {/* Study Card */}
        <button
          onClick={() => setShowStudyCategories(!showStudyCategories)}
          className="bg-white rounded-lg shadow-lg p-6 md:p-8 hover:shadow-xl transition-all duration-300 hover:scale-105 animate-fade-in w-full text-left"
        >
          <div className="text-center">
            <div className="text-4xl md:text-6xl mb-4">📖</div>
            <h3 className="text-xl md:text-2xl font-bold text-gray-800 mb-4">
              ศึกษาข้อสอบและคำตอบ
            </h3>
            <div className="text-sm md:text-base text-gray-600 mb-6">
              <p>ดูข้อสอบพร้อมคำตอบ</p>
              <p className="hidden md:block">ศึกษาก่อนทำข้อสอบจริง</p>
            </div>
            <div className="text-xs md:text-sm text-gray-500">
              {showStudyCategories ? '🔼 คลิกเพื่อซ่อนหมวดหมู่' : '🔽 คลิกเพื่อเลือกหมวดหมู่'}
            </div>
          </div>
        </button>
      </div>

      {/* Categories Grid for Exam */}
      {showCategories && (
        <div className="bg-white rounded-lg shadow-lg p-6 md:p-8 animate-fade-in">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl md:text-2xl font-bold text-gray-800">
              หมวดหมู่ข้อสอบ - ทดสอบ
            </h3>
            <button
              onClick={() => setShowCategories(false)}
              className="text-gray-500 hover:text-gray-700 transition-colors"
              title="ซ่อนหมวดหมู่"
            >
              ✕
            </button>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 md:gap-4">
            {categories.map((category, index) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className="bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-3 md:p-4 text-left transition-all duration-300 hover:scale-105 hover:shadow-md animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="text-xl md:text-2xl mb-2">{category.icon}</div>
                <h4 className="font-semibold text-gray-800 mb-1 text-xs md:text-sm leading-tight">
                  {category.name}
                </h4>
                <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                  {category.description}
                </p>
                <div className="flex justify-between items-center mb-2">
                  <p className="text-xs text-blue-600 font-medium">
                    {category.count} ข้อ
                  </p>
                  <div className="text-xs text-gray-400">
                    {Math.ceil(category.count * 0.9)} ข้อผ่าน
                  </div>
                </div>
                <div className="text-center">
                  <span className="inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full font-medium">
                    📝 ทดสอบ
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Categories Grid for Study */}
      {showStudyCategories && (
        <div className="bg-white rounded-lg shadow-lg p-6 md:p-8 animate-fade-in">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl md:text-2xl font-bold text-gray-800">
              หมวดหมู่ข้อสอบ - ศึกษา
            </h3>
            <button
              onClick={() => setShowStudyCategories(false)}
              className="text-gray-500 hover:text-gray-700 transition-colors"
              title="ซ่อนหมวดหมู่"
            >
              ✕
            </button>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 md:gap-4">
            {categories.map((category, index) => (
              <button
                key={category.id}
                onClick={() => window.location.href = `/study?category=${category.id}`}
                className="bg-gray-50 hover:bg-green-50 border border-gray-200 hover:border-green-300 rounded-lg p-3 md:p-4 text-left transition-all duration-300 hover:scale-105 hover:shadow-md animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="text-xl md:text-2xl mb-2">{category.icon}</div>
                <h4 className="font-semibold text-gray-800 mb-1 text-xs md:text-sm leading-tight">
                  {category.name}
                </h4>
                <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                  {category.description}
                </p>
                <div className="flex justify-between items-center mb-2">
                  <p className="text-xs text-green-600 font-medium">
                    {category.count} ข้อ
                  </p>
                  <div className="text-xs text-gray-400">
                    ศึกษาพร้อมคำตอบ
                  </div>
                </div>
                <div className="text-center">
                  <span className="inline-block px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full font-medium">
                    📖 ศึกษา
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Modals */}
      {showMockExamModal && (
        <MockExamModal onClose={() => setShowMockExamModal(false)} />
      )}

      {selectedCategory && (
        <CategoryModal
          categoryId={selectedCategory}
          onClose={() => setSelectedCategory(null)}
        />
      )}
    </div>
  );
}
