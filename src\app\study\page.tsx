'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { categories, getCategoryById } from '../data';
import Image from 'next/image';

export default function StudyPage() {
  const searchParams = useSearchParams();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [showAnswer, setShowAnswer] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewedQuestions, setViewedQuestions] = useState<Set<number>>(new Set());

  useEffect(() => {
    const categoryParam = searchParams.get('category');
    if (categoryParam) {
      setSelectedCategory(categoryParam);
    }
  }, [searchParams]);

  const category = selectedCategory ? getCategoryById(selectedCategory) : null;
  const allQuestions = category?.data || [];
  
  // Filter questions based on search term
  const questions = searchTerm 
    ? allQuestions.filter(question => 
        question.question.some(q => q.text?.toLowerCase().includes(searchTerm.toLowerCase())) ||
        question.options.some(option => option.text?.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    : allQuestions;
    
  const currentQuestion = questions[currentQuestionIndex];

  useEffect(() => {
    // Mark current question as viewed
    if (currentQuestion) {
      setViewedQuestions(prev => new Set([...prev, currentQuestionIndex]));
    }
  }, [currentQuestionIndex, currentQuestion]);

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setCurrentQuestionIndex(0);
    setShowAnswer(false);
    setSearchTerm('');
    setViewedQuestions(new Set());
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentQuestionIndex(0);
    setShowAnswer(false);
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setShowAnswer(false);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      setShowAnswer(false);
    }
  };

  const handleQuestionSelect = (index: number) => {
    setCurrentQuestionIndex(index);
    setShowAnswer(false);
  };

  if (!selectedCategory) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8 animate-fade-in">
          <h1 className="text-2xl md:text-4xl font-bold text-gray-800 mb-4">
            📖 ศึกษาข้อสอบและคำตอบ
          </h1>
          <p className="text-base md:text-lg text-gray-600 mb-6 px-4">
            เลือกหมวดหมู่ที่ต้องการศึกษาข้อสอบและคำตอบ
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6 md:p-8 animate-fade-in">
          <h3 className="text-xl md:text-2xl font-bold text-gray-800 mb-6 text-center">
            เลือกหมวดหมู่ข้อสอบ
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 md:gap-4">
            {categories.map((category, index) => (
              <button
                key={category.id}
                onClick={() => handleCategorySelect(category.id)}
                className="bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-3 md:p-4 text-left transition-all duration-300 hover:scale-105 hover:shadow-md animate-fade-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="text-xl md:text-2xl mb-2">{category.icon}</div>
                <h4 className="font-semibold text-gray-800 mb-1 text-xs md:text-sm leading-tight">
                  {category.name}
                </h4>
                <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                  {category.description}
                </p>
                <div className="flex justify-between items-center">
                  <p className="text-xs text-blue-600 font-medium">
                    {category.count} ข้อ
                  </p>
                  <div className="text-xs text-green-600 font-medium">
                    📚 ศึกษา
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div className="text-center mt-8">
          <button
            onClick={() => window.location.href = '/'}
            className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
          >
            🏠 กลับหน้าหลัก
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 mb-4 md:mb-6 animate-fade-in">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4">
          <div className="mb-4 lg:mb-0">
            <h1 className="text-xl md:text-2xl font-bold text-gray-800 mb-1">
              📖 {category?.name}
            </h1>
            <p className="text-sm md:text-base text-gray-600">
              {category?.description}
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setSelectedCategory(null)}
              className="bg-gray-500 text-white px-3 py-2 rounded-lg hover:bg-gray-600 transition-colors text-sm"
            >
              ← เลือกหมวดหมู่อื่น
            </button>
            <button
              onClick={() => window.location.href = '/'}
              className="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              🏠 หน้าหลัก
            </button>
          </div>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${questions.length > 0 ? ((currentQuestionIndex + 1) / questions.length) * 100 : 0}%` }}
          ></div>
        </div>
        
        <div className="flex flex-col sm:flex-row sm:justify-between text-xs md:text-sm text-gray-600 gap-2">
          <span>ข้อ {currentQuestionIndex + 1} จาก {questions.length}</span>
          <span>หมวดหมู่: {category?.name}</span>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 mb-4 md:mb-6 animate-fade-in">
        <div className="flex flex-col md:flex-row gap-4 items-center">
          <div className="flex-1">
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
              🔍 ค้นหาข้อสอบ:
            </label>
            <input
              type="text"
              id="search"
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              placeholder="พิมพ์คำที่ต้องการค้นหาในข้อสอบหรือตัวเลือก..."
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          {searchTerm && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                พบ {questions.length} ข้อ
              </span>
              <button
                onClick={() => handleSearch('')}
                className="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600 transition-colors"
              >
                ล้าง
              </button>
            </div>
          )}
        </div>
      </div>

      {questions.length === 0 && searchTerm && (
        <div className="bg-white rounded-lg shadow-lg p-8 text-center animate-fade-in">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-bold text-gray-800 mb-2">ไม่พบข้อสอบที่ค้นหา</h3>
          <p className="text-gray-600 mb-4">
            ไม่พบข้อสอบที่มีคำว่า "<span className="font-semibold">{searchTerm}</span>"
          </p>
          <button
            onClick={() => handleSearch('')}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            แสดงข้อสอบทั้งหมด
          </button>
        </div>
      )}

      {questions.length > 0 && currentQuestion && (
        <div className="grid lg:grid-cols-4 gap-4 md:gap-6">
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 animate-fade-in">
              <div className="mb-4 md:mb-6">
                <h2 className="text-lg md:text-xl font-bold text-gray-800 mb-3 md:mb-4">
                  {currentQuestion.numbers}
                </h2>
                <div className="bg-gray-50 rounded-lg p-3 md:p-4">
                  {currentQuestion.question.map((item, index) => (
                    <div key={index} className="mb-4">
                      {item.text && (
                        <p className="text-gray-800 leading-relaxed">{item.text}</p>
                      )}
                      {item.image && (
                        <div className="mt-4 flex justify-center">
                          <div className="relative max-w-md w-full">
                            <Image
                              src={item.image}
                              alt={`Question ${currentQuestionIndex + 1} image`}
                              width={400}
                              height={300}
                              className="rounded-lg shadow-md object-contain w-full h-auto"
                              unoptimized
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2 md:space-y-3 mb-6">
                <h3 className="text-base md:text-lg font-semibold text-gray-800 mb-3 md:mb-4">
                  ตัวเลือก:
                </h3>
                {currentQuestion.options.map((option, index) => {
                  const isCorrectAnswer = currentQuestion.correctAnswer === index;
                  
                  return (
                    <div
                      key={index}
                      className={`w-full text-left p-4 rounded-lg border-2 transition-all duration-200 ${
                        showAnswer && isCorrectAnswer
                          ? 'border-green-500 bg-green-50 text-green-800'
                          : 'border-gray-200 bg-white'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center text-sm font-semibold ${
                          showAnswer && isCorrectAnswer
                            ? 'border-green-500 bg-green-500 text-white'
                            : 'border-gray-400 text-gray-400'
                        }`}>
                          {String.fromCharCode(65 + index)}
                        </div>
                        <div className="flex-1">
                          {option.text && (
                            <p className="text-gray-800 leading-relaxed">{option.text}</p>
                          )}
                          {option.image && (
                            <div className="mt-2">
                              <Image
                                src={option.image}
                                alt={`Option ${String.fromCharCode(65 + index)}`}
                                width={200}
                                height={150}
                                className="rounded-md shadow-sm object-contain"
                                unoptimized
                              />
                            </div>
                          )}
                        </div>
                        {showAnswer && isCorrectAnswer && (
                          <div className="flex-shrink-0 text-green-600 font-bold text-lg">
                            ✓
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>

              <div className="mb-6 text-center">
                <button
                  onClick={() => setShowAnswer(!showAnswer)}
                  className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                    showAnswer
                      ? 'bg-gray-300 text-gray-700 hover:bg-gray-400'
                      : 'bg-green-600 text-white hover:bg-green-700'
                  }`}
                >
                  {showAnswer ? '🙈 ซ่อนคำตอบ' : '👁️ แสดงคำตอบ'}
                </button>
              </div>

              {showAnswer && (
                <div className="p-4 bg-green-50 border border-green-300 rounded-lg animate-fade-in mb-6">
                  <h4 className="font-semibold text-green-800 mb-2">
                    ✅ คำตอบที่ถูกต้อง:
                  </h4>
                  <p className="text-green-700">
                    <span className="font-bold">
                      {String.fromCharCode(65 + currentQuestion.correctAnswer)}.
                    </span>
                    {currentQuestion.options[currentQuestion.correctAnswer].text && (
                      <span className="ml-2">
                        {currentQuestion.options[currentQuestion.correctAnswer].text}
                      </span>
                    )}
                  </p>
                </div>
              )}

              <div className="flex flex-col sm:flex-row justify-between gap-3">
                <button
                  onClick={handlePreviousQuestion}
                  disabled={currentQuestionIndex === 0}
                  className="bg-gray-300 text-gray-700 px-4 md:px-6 py-3 rounded-lg font-semibold hover:bg-gray-400 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed order-2 sm:order-1"
                >
                  ← ข้อก่อนหน้า
                </button>
                
                <button
                  onClick={handleNextQuestion}
                  disabled={currentQuestionIndex === questions.length - 1}
                  className="bg-blue-600 text-white px-4 md:px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed order-1 sm:order-2"
                >
                  ข้อถัดไป →
                </button>
              </div>
            </div>
          </div>

          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-4 md:p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                ข้อสอบทั้งหมด
              </h3>
              
              <div className="mb-4 space-y-2 text-xs">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-blue-600 rounded"></div>
                  <span className="text-gray-600">ข้อปัจจุบัน</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-green-100 border border-green-300 rounded"></div>
                  <span className="text-gray-600">ดูแล้ว</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-gray-100 border border-gray-300 rounded"></div>
                  <span className="text-gray-600">ยังไม่ดู</span>
                </div>
              </div>

              <div className="grid grid-cols-5 gap-2 mb-4">
                {questions.map((_, index) => {
                  const isViewed = viewedQuestions.has(index);
                  const isCurrent = index === currentQuestionIndex;
                  
                  return (
                    <button
                      key={index}
                      onClick={() => handleQuestionSelect(index)}
                      className={`w-10 h-10 rounded-lg border-2 text-sm font-semibold transition-all duration-200 hover:scale-105 ${
                        isCurrent
                          ? 'bg-blue-600 text-white border-blue-600'
                          : isViewed
                          ? 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200'
                          : 'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200'
                      }`}
                      title={`ข้อ ${index + 1} - ${
                        isCurrent ? 'ข้อปัจจุบัน' :
                        isViewed ? 'ดูแล้ว' : 'ยังไม่ดู'
                      }`}
                    >
                      {index + 1}
                    </button>
                  );
                })}
              </div>

              <div className="p-3 bg-gray-50 rounded-lg text-sm space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">ข้อสอบทั้งหมด:</span>
                  <span className="font-semibold">{questions.length} ข้อ</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">ข้อปัจจุบัน:</span>
                  <span className="font-semibold text-blue-600">{currentQuestionIndex + 1}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">ดูแล้ว:</span>
                  <span className="font-semibold text-green-600">{viewedQuestions.size} ข้อ</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">ยังไม่ดู:</span>
                  <span className="font-semibold text-red-600">{questions.length - viewedQuestions.size} ข้อ</span>
                </div>
                
                <div className="mt-3">
                  <div className="flex justify-between text-xs text-gray-600 mb-1">
                    <span>ความคืบหน้า</span>
                    <span className="font-semibold">{Math.round((viewedQuestions.size / questions.length) * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${(viewedQuestions.size / questions.length) * 100}%` }}
                    ></div>
                  </div>
                </div>
                
                {viewedQuestions.size === questions.length && questions.length > 0 && (
                  <div className="mt-3 p-2 bg-green-100 border border-green-300 rounded-lg text-center">
                    <span className="text-green-800 text-xs font-medium">
                      🎉 ศึกษาครบทุกข้อแล้ว!
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
